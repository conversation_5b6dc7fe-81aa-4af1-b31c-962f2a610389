import { Tooltip } from "antd";
import { Select as AntdSelect } from "antd";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { RenderElementProps } from "slate-react";

import { get, post } from "@/request";

// API Configuration Interface
export interface ApiConfig {
  url: string;
  method?: "GET" | "POST";
  headers?: Record<string, string>;
  params?: Record<string, unknown>;
  data?: Record<string, unknown>;
  mapper?: {
    label: string;
    value: string;
  };
}

interface ApiOptionResponse {
  data?: { label: string; value: string }[];
  [key: string]: unknown;
}

export interface SelectElementProps {
  placeholder?: string;
  options?: Array<{ label: string; value: string }>;
  apiConfig?: ApiConfig;
  defaultValue?: string;
  tooltips?: string;
  disabled?: boolean;
  style?: React.CSSProperties;
  mode?: "multiple" | "tags";
}

// Select Element Component
export const SelectElement: React.FC<RenderElementProps> = ({ element }) => {
  const {
    placeholder,
    options: staticOptions,
    apiConfig,
    defaultValue,
    tooltips,
    disabled,
    style,
    mode,
  } = element as SelectElementProps;

  const [selectedValue, setSelectedValue] = useState<string | string[] | null>(!defaultValue ? null : defaultValue);
  const [apiOptions, setApiOptions] = useState<{ label: string; value: unknown }[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Merge static and API options
  const allOptions = [...(staticOptions || []), ...apiOptions];

  // Fetch options from API
  const fetchOptionsFromApi = useCallback(async () => {
    if (!apiConfig) return;

    try {
      setLoading(true);
      setError(null);

      const controller = new AbortController();
      abortControllerRef.current = controller;

      const { url, method = "GET", headers, params, data } = apiConfig;

      let response;
      const requestOptions = {
        signal: controller.signal,
        headers,
      };

      switch (method.toUpperCase()) {
        case "GET":
          response = await get<ApiOptionResponse>(url, params, requestOptions);
          break;
        case "POST":
          response = await post<ApiOptionResponse>(url, data, { ...requestOptions, params });
          break;
        default:
          throw new Error(`Unsupported HTTP method: ${method}`);
      }

      const responseData = response?.data.data;
      let options: { label: string; value: unknown }[] = [];

      if (Array.isArray(responseData)) {
        if (apiConfig.mapper) {
          options = responseData.map((item: any) => {
            const labelKey = apiConfig.mapper?.label;
            const valueKey = apiConfig.mapper?.value;
            if (labelKey !== undefined && valueKey !== undefined) {
              return { label: item[labelKey], value: item[valueKey] };
            }
            return item;
          });
        } else {
          options = responseData;
        }
      }

      setApiOptions(options);
    } catch (err: unknown) {
      if ((err as Error).name === "AbortError" || (err as Error).message === "canceled") {
        return;
      }
      console.error("Failed to fetch options from API:", err);
      setError((err as Error).message || "Failed to fetch options");
    } finally {
      setLoading(false);
      abortControllerRef.current = null;
    }
  }, [apiConfig]);

  const handleChange = (value: string | string[]) => {
    setSelectedValue(value);
    if (Array.isArray(value)) {
      (element.defaultValue as any) = value.join();
    } else {
      (element.defaultValue as any) = value || null;
    }
  };

  useEffect(() => {
    fetchOptionsFromApi();

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [fetchOptionsFromApi]);

  return (
    <span contentEditable={false} className="ag:px-1">
      <Tooltip title={tooltips}>
        <AntdSelect
          value={selectedValue}
          onChange={handleChange}
          disabled={disabled || loading}
          placeholder={error ? `加载失败: ${error}` : loading ? "加载中..." : placeholder}
          options={allOptions}
          size="small"
          className="ag:min-w-30"
          showSearch
          allowClear
          filterOption={(input, option) => (option?.label ?? "").toLowerCase().includes(input.toLowerCase())}
          variant="filled"
          style={style}
          mode={mode}
        ></AntdSelect>
      </Tooltip>
    </span>
  );
};

export default SelectElement;
