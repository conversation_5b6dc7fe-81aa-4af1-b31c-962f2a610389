// mock/api.mock.ts
import { defineMock } from "vite-plugin-mock-dev-server/helper";
import { MessageMocker } from "@cscs-agent/mock";
var api_mock_default = defineMock([
  {
    url: "x/api/chat/test",
    method: "POST",
    response: (req, res) => {
      const message = `
        \u5DE5\u5177\u8C03\u7528
    <message-embedded>
      <state>
        <set>
          <strategy>replace</strategy>
          <path>tool1Status</path>
          <value>loading</value>
        </set>
      </state>

      <widget>
        <code>@BuildIn/Tool</code>
        <props>
            <name>\u67E5\u8BE2\u5DE5\u5177\u8C03\u7528</name>
            <result>\u5DE5\u51771\u8F93\u51FA\u7ED3\u679Cxxxxx</result>
            <status>{{state.tool1Status}}</status>
        </props>
      </widget>
    </message-embedded>
    <message-embedded>
      <state>
        <set>
          <strategy>replace</strategy>
          <path>tool1Status</path>
          <value>success</value>
        </set>
      </state>
    </message-embedded>
    <message-embedded>
      <state>
        <set>
          <strategy>replace</strategy>
          <path>tool2Status</path>
          <value>loading</value>
        </set>
      </state>
      <widget>
        <code>@BuildIn/Tool</code>
        <props>
            <name>\u67E5\u8BE2\u5DE5\u5177\u8C03\u7528</name>
            <result>\u5DE5\u51772\u8F93\u51FA\u7ED3\u679Cxxxxx</result>
            <status>{{state.tool2Status}}</status>
        </props>
      </widget>
    </message-embedded>
    <message-embedded>
      <state>
        <set>
          <strategy>replace</strategy>
          <path>tool2Status</path>
          <value>success</value>
        </set>
      </state>
    </message-embedded>
    11111111111111111111111
      `;
      const mocker = new MessageMocker(req, res, 10);
      mocker.start(message);
    }
  }
]);
export {
  api_mock_default as default
};
