import React, { PropsWithChildren } from "react";

import { AgentChatConfig, HostWrapper } from "@cscs-agent/core";

const AgentEmbedProvider: React.FC<PropsWithChildren<{ agentChatConfig: AgentChatConfig; agentCode: string }>> = (
  props,
) => {
  const { agentChatConfig, agentCode, children } = props;

  return (
    <HostWrapper agentChatConfig={agentChatConfig} embedded={true} agentCode={agentCode}>
      {children}
    </HostWrapper>
  );
};

export default AgentEmbedProvider;
