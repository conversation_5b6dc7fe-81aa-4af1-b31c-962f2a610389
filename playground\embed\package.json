{"name": "embedded", "version": "1.0.0", "private": true, "description": "", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "watch": "vite build --mode development --minify false --watch", "lint": "eslint ./src --ext .ts,.tsx --fix"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@cscs-agent/embed": "workspace:*", "@cscs-agent/icons": "workspace:*", "antd": "4.24.15", "dayjs": "^1.11.13", "react": "^16.14.0", "react-dom": "^16.14.0", "tailwindcss": "^4.1.4"}, "devDependencies": {"@eslint/js": "^9.25.1", "@rollup/plugin-typescript": "^12.1.2", "@tailwindcss/vite": "^4.1.4", "@types/react": "^16.9.34", "@types/react-dom": "^16.9.8", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^15.15.0", "typescript": "^5.8.2", "vite": "^6.3.0", "vite-plugin-mock-dev-server": "^1.9.1"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.9.0"}