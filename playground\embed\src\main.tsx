// import "@@/@cscs-agent/core/dist/agent-tailwind.css";
// import "@@/@cscs-agent/presets/dist/presets-tailwind.css";
// import "@@/@cscs-agent/agents/dist/agents-tailwind.css";
// import "@@/@cscs-agent/icons/dist/icons.css";
// import "virtual:app-dep-info";

import dayjs from "dayjs";
import React from "react";
import ReactDOM from "react-dom";

import { initAgent } from "@cscs-agent/embed";

import Home from "./pages/home";

// import "./styles.css";

dayjs.locale("zh-cn");

initAgent({
  defaultRequestInterceptorOptions: {
    prefix: "/agent-api",
  },
  defaultErrorHandlerOption: {
    redirectToLogin: false,
  },
}).then(() => {
  ReactDOM.render(<Home />, document.getElementById("root"));
});
