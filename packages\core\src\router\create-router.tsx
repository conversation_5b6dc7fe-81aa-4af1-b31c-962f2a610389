import React from "react";
import { createBrowserRouter, RouteObject } from "react-router-dom";

import { getLoginUrl } from "@/core/common/vars";
import HostWrapper from "@/core/host/HostWrapper";
import { AgentChatConfig, ExtendRouterObject } from "@/types";

export interface RouterConfig {
  pages?: {
    login?: {
      enable: boolean;
      path?: string;
      Component?: React.FC;
    };
    home?: {
      path?: string;
      Component?: React.FC;
    };
    chat?: {
      path?: string;
      Component?: React.FC;
    };
    agentHome?: {
      path?: string;
      Component?: React.FC;
    };
    conversationHistory?: {
      path?: string;
      Component?: React.FC;
    };
  };
  routes?: ExtendRouterObject[];
  rootRoutes?: ExtendRouterObject[];
  authGuard?: () => boolean;
  loginUrl?: string;
  basename?: string;
}

export interface CreateRouterOptions {
  basename?: string;
}

export async function createDefaultRouter(config: RouterConfig, agentChatConfig: AgentChatConfig) {
  const { pages = {}, routes = [], rootRoutes = [], authGuard, loginUrl, basename = "" } = config;
  const HomeComponent = pages.home?.Component;
  const loader = () => {
    if (config.authGuard) {
      const isAuthed = authGuard ? authGuard() : true;
      if (!isAuthed) {
        location.href = loginUrl ?? getLoginUrl();
      }
    }
    return null;
  };

  // 添加 auth 校验
  const $rootRoutes = rootRoutes.map((route) => {
    const { auth, ...restProps } = route;
    if (auth && !route.loader) {
      return {
        ...restProps,
        loader,
      };
    } else {
      return route;
    }
  });

  const $routes: any[] = [
    ...$rootRoutes,
    {
      path: "/",
      element: <HostWrapper agentChatConfig={agentChatConfig}>{HomeComponent && <HomeComponent />}</HostWrapper>,
      // 鉴权Guard
      loader,
      children: [
        {
          path: "chat/:id",
          Component: pages.chat?.Component,
        },
        {
          path: "agent/:code",
          Component: pages.agentHome?.Component,
        },
        {
          path: "conversations",
          Component: pages.conversationHistory?.Component,
        },
        ...routes,
      ],
    },
  ];

  if (pages.login?.enable) {
    const loginPageRoute: RouteObject = {
      path: "/login",
      Component: pages.login?.Component,
    };
    $routes.unshift(loginPageRoute);
  }

  const router = createBrowserRouter($routes, {
    basename,
  });

  return router as any;
}
