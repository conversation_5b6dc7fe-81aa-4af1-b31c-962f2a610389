{"name": "@cscs-agent/core", "version": "0.5.0", "description": "", "main": "dist/index.es.js", "module": "dist/index.es.js", "types": "dist/index.d.ts", "type": "module", "files": ["dist"], "scripts": {"dev": "vite build --mode development --minify false", "watch": "vite build --mode development --minify false --watch", "build": "vite build", "lint": "eslint ./src --ext .ts,.tsx --fix", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {"@types/react-copy-to-clipboard": "^5.0.7", "@types/react-katex": "^3.0.4", "ahooks": "^3.8.5", "axios": "^1.6.7", "dayjs": "^1.11.13", "dot-prop": "^9.0.0", "eventemitter3": "^5.0.1", "fast-xml-parser": "^5.2.3", "handlebars": "^4.7.8", "jotai": "^2.12.3", "katex": "^0.16.22", "mermaid": "^11.6.0", "nanoid": "^5.1.5", "penpal": "^7.0.0", "react-copy-to-clipboard": "^5.1.0", "react-infinite-scroll-component": "^6.1.0", "react-katex": "^3.1.0", "react-markdown": "^8.0.7", "rehype-highlight": "^7.0.2", "rehype-katex": "^7.0.1", "rehype-raw": "^7.0.0", "remark-gfm": "^3.0.1", "remark-math": "^6.0.0", "slate": "^0.117.2", "slate-dom": "^0.117.4", "slate-history": "^0.113.1", "slate-hyperscript": "^0.100.0", "slate-react": "^0.117.3", "vite-plugin-static-copy": "^3.0.0"}, "devDependencies": {"@ant-design/icons": "^5.6.1", "@cscs-agent/icons": "workspace:^0.1.0", "@eslint/js": "^9.25.1", "@rollup/plugin-typescript": "^12.1.2", "@tailwindcss/vite": "^4.1.4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "22.15.0", "@types/react": "^16.9.34", "@types/react-dom": "^16.9.9", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "antd": "^5.24.3", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^15.15.0", "jsdom": "^26.1.0", "react": ">=16.12.0", "react-dom": ">=16.12.0", "react-router-dom": ">=6.30.1", "tailwindcss": "^4.1.4", "typescript": "^5.8.2", "vite": "^6.2.0", "vite-plugin-dts": "^4.5.3", "vite-plugin-svgr": "^4.3.0", "vitest": "^3.1.3"}, "peerDependencies": {"@ant-design/icons": "^5.6.1", "antd": "^5.24.3", "react": ">=16.12.0", "react-dom": ">=16.12.0", "react-router-dom": ">=6.30.1"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.9.0"}