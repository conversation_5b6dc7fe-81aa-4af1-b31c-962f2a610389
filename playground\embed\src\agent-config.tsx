import "@@/@cscs-agent/embed/dist/agent-tailwind.css";
import "@@/@cscs-agent/embed/dist/presets-tailwind.css";
import "@@/@cscs-agent/embed/dist/icons.css";

import { Button } from "antd";
import React from "react";

import { SearchOutlined, SortAscendingOutlined } from "@ant-design/icons";

import FormDemo from "./widgets/form";

// import InsertTag from "./widgets/insert-tag";
// import InsertText from "./widgets/insert-text";
// import OpenSidePanelMessage from "./widgets/open-side-panel-message";

export const config = {
  agents: [
    {
      name: "测试智能体",
      code: "default",
      message: {
        blocks: {
          widgets: [
            {
              code: "@DynamicPage/PreviewButton",
              component: () => {
                return <Button>PreviewWidget</Button>;
              },
            },
            // {
            //   code: "@BuildIn/ThoughtChain",
            //   component: <PERSON><PERSON>hain,
            // },
            // {
            //   code: "@Test/FormDemo",
            //   component: FormDemo,
            // },
            // {
            //   code: "@BuildIn/Message",
            //   component: Message,
            // },
            // {
            //   code: "@Test/OpenSidePanelMessage",
            //   component: OpenSidePanelMessage,
            // },
          ],
        },
        slots: {
          footer: {
            widgets: [
              // {
              //   code: "Copy",
              //   component: Copy,
              //   role: Role.AI,
              // },
              // {
              //   code: "Rating",
              //   component: Rating,
              //   role: Role.AI,
              // },
            ],
          },
        },
      },
      prompts: [
        {
          icon: <SearchOutlined />,
          title: "字段查询字段查询字段查询",
          description: "根据字段查询列表",
          prompt: "请查询{{[企业名称]}}获取企业信息",
        },
        {
          icon: <SortAscendingOutlined />,
          title: "排序",
          description: "配置列表排序",
          prompt: "请对列表进行排序",
        },
      ],
      commands: [
        {
          name: "",
          action: () => {},
        },
      ],
      suggestions: [],
      sender: {
        slots: {
          headerPanel: {
            widgets: [
              // {
              //   code: "PromptTemplate",
              //   component: PromptTemplate,
              // },
            ],
          },
          header: {
            widgets: [
              // {
              //   code: "InsertText",
              //   component: InsertText,
              // },
              // {
              //   code: "InsertTag",
              //   component: InsertTag,
              // },
            ],
          },
          footer: {
            widgets: [],
          },
        },
      },
      sidePanel: {
        render: {
          widgets: [
            // {
            //   code: "@BuildIn/Message",
            //   component: Message,
            // },
          ],
        },
      },
      request: {
        chat: {
          url: "/chat/test",
          headers: {},
          method: "POST",
        },
      },
    },
  ],
};
