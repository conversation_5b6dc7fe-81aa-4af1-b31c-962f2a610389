{"name": "@cscs-agent/builder", "version": "0.5.0", "description": "Infrastructure for build Agent project", "type": "module", "files": ["lib", "tpl", "README.md"], "main": "lib/index.js", "bin": {"agent-builder": "./bin/cli.js"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src --ext .ts,.js --fix"}, "dependencies": {"@rollup/plugin-typescript": "^12.1.2", "@tailwindcss/vite": "^4.1.4", "@vitejs/plugin-react": "^4.3.4", "commander": "^12.1.0", "esbuild": "^0.25.6", "gradient-string": "^3.0.0", "handlebars": "^4.7.8", "vite": "^6.3.0", "vite-plugin-mock-dev-server": "^1.9.1"}, "devDependencies": {"@types/node": "^22.15.0", "eslint": "^9.25.1", "jsdom": "^26.1.0", "typescript": "^5.8.2"}, "keywords": ["cscs-agent"], "author": "CSCS Team", "license": "ISC", "packageManager": "pnpm@10.9.0", "engines": {"node": ">=18.0.0"}}