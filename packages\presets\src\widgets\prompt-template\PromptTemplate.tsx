import { useRequest } from "ahooks";
import { <PERSON><PERSON>, Col, Modal, Row, Spin } from "antd";
import React, { useEffect, useMemo, useState } from "react";
import SortableList, { SortableItem } from "react-easy-sort";

import { PresetsCommand } from "@/command";
import { StandardResponse, del, get, put, useActiveAgentCode, useSubscribeCommand } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";

import AddUpdateUserPrompt from "./AddUpdateUserPrompt";
import PromptCard from "./PromptCard";

// 提示模板数据结构定义
export interface PromptTemplate {
  id: number;
  title: string;
  description: string;
  prompt: string;
  category: string;
  deleted: boolean;
  preview?: string; // 预览图片地址
  type: string;
}

/**
 * 提示模板组件 - 管理和展示用户自定义模板和系统模板
 * 支持分类查看、拖拽排序、增删改查等功能
 */
const TemplateModal: React.FC = () => {
  const [activeAgentCode] = useActiveAgentCode();

  // 模板分类：全部、自定义模板、系统模板
  const [categories] = useState<string[]>(["全部", "自定义模板"]);
  const [userTemplates, setUserTemplates] = useState<PromptTemplate[]>([]);
  const [activeCategory, setActiveCategory] = useState<string>("全部");
  const [updating, setUpdating] = useState<boolean>(false); // 拖拽排序更新状态

  useSubscribeCommand(PresetsCommand.ReloadUserPrompt, () => {
    getAllUserTemplates();
  });

  // 获取系统模板数据
  const { data: systemTemplates, run: getSystemPrompts } = useRequest(
    async () => {
      const result = await get<StandardResponse<PromptTemplate[]>>("/prompt-template/system", {
        agent_code: activeAgentCode,
      });
      return result.data.data ?? [];
    },
    {
      manual: true,
    },
  );

  // 获取用户自定义模板数据
  const getAllUserTemplates = async () => {
    const result = await get<StandardResponse<PromptTemplate[]>>("/user-prompt-template", {
      agent_code: activeAgentCode,
    });
    setUserTemplates(result.data.data ?? []);
  };

  // 更新用户模板排序
  const updateUserPromptOrder = async (id: number, prevId: number) => {
    await put<StandardResponse<PromptTemplate[]>>(`/user-prompt-template/${id}/order`, {
      prev_id: prevId ?? null,
    });
    return true;
  };

  // 当代理配置变化时重新加载数据
  useEffect(() => {
    getAllUserTemplates();
    getSystemPrompts();
    setActiveCategory("全部");
  }, [activeAgentCode]);

  // 合并用户模板和系统模板
  const allTemplates = useMemo(() => {
    return [...userTemplates, ...(systemTemplates ?? [])];
  }, [userTemplates, systemTemplates]);

  // 处理拖拽排序结束事件
  const onSortEnd = (oldIndex: number, newIndex: number) => {
    if (updating) return;
    setUpdating(true);
    const originalUserTemplates = [...userTemplates];

    // 更新服务端排序
    updateUserPromptOrder(userTemplates[oldIndex].id, userTemplates[newIndex]?.id)
      .then((success) => {
        if (!success) return;
      })
      .catch(() => {
        // 失败时恢复原始顺序
        setUserTemplates(originalUserTemplates);
      })
      .finally(() => {
        setUpdating(false);
      });

    // 立即更新本地状态以提供即时反馈
    setUserTemplates((array) => {
      // 移动数组位置，从 oldIndex 到 newIndex
      const newArray = [];
      for (let i = 0; i < array.length; i++) {
        if (i === newIndex) {
          newArray.push(array[oldIndex]);
          continue;
        }

        if (oldIndex > newIndex) {
          if (i > newIndex && i <= oldIndex) {
            newArray.push(array[i - 1]);
          } else {
            newArray.push(array[i]);
          }
        } else {
          if (i >= oldIndex && i < newIndex) {
            newArray.push(array[i + 1]);
            continue;
          } else {
            newArray.push(array[i]);
          }
        }
      }
      return newArray;
    });
  };

  // 处理用户模板创建/更新成功回调
  const handleUserPromptCreated = () => {
    getAllUserTemplates();
  };

  // 删除用户模板
  const handleDelPrompt = (id: number) => {
    del(`/user-prompt-template/${id}`).then(() => {
      // 先标记为已删除，添加淡出动画效果
      setUserTemplates((prev) => {
        return prev.map((i) => {
          if (i.id === id) {
            i.deleted = true;
          }
          return i;
        });
      });
      // 延迟后重新获取数据，完成删除
      setTimeout(() => {
        getAllUserTemplates();
      }, 300);
    });
  };

  return (
    <div className="pts:bg-white pts:w-full">
      <div className="pts:flex pts:mb-3">
        {categories.map((name) => (
          <div
            className={`pts:mr-2 pts:px-2 pts:py-1 pts:text-sm pts:rounded-sm pts:cursor-pointer ${activeCategory === name ? "pts:bg-[rgba(11,104,230,0.10)] pts:text-primary" : "pts:bg-[rgba(172,174,189,0.10)] pts:text-black-65"} `}
            key={name}
            onClick={() => setActiveCategory(name)}
          >
            {name}
          </div>
        ))}
      </div>
      <div className="pts:-mr-1 pts:pr-1 pts:h-[300px] pts:overflow-x-hidden pts:overflow-y-auto mini-scrollbar">
        <div className={activeCategory !== "全部" ? "pts:hidden" : ""}>
          <Row gutter={[8, 8]}>
            {allTemplates?.map((item, index) => (
              <Col span={6} key={index}>
                <PromptCard data={item} />
              </Col>
            ))}
          </Row>
        </div>

        <SortableList
          onSortEnd={onSortEnd}
          draggedItemClassName="dragged"
          className={activeCategory !== "自定义模板" ? "pts:hidden" : ""}
        >
          <Spin spinning={updating}>
            <Row gutter={[8, 8]}>
              <Col span={6}>
                <AddUpdateUserPrompt onSuccess={handleUserPromptCreated}>
                  <div className="pts:flex pts:justify-center pts:items-center pts:bg-white pts:p-3 pts:border pts:border-gray-200 pts:hover:border-blue-500 pts:rounded-sm pts:h-[84px] pts:cursor-pointer">
                    <div className="pts:bg-[#0B68E6] pts:rounded-[8px] pts:w-[15px] pts:h-[15px] pts:text-white pts:text-sm pts:text-center pts:leading-[15px] pts:-align-[0.1em]">
                      <span className="pts:block pts:leading-3">+</span>
                    </div>
                    <div className="pts:ml-2 pts:text-primary pts:text-sm">新建自定义模板</div>
                  </div>
                </AddUpdateUserPrompt>
              </Col>
              {userTemplates?.map((item) => (
                <SortableItem key={item.id}>
                  <Col span={6}>
                    <PromptCard
                      data={item}
                      actions={
                        <div className="pts:hidden pts:group-hover:block pts:min-w-[52px] pts:max-w-[52px]">
                          <AddUpdateUserPrompt id={item.id} data={item} onSuccess={handleUserPromptCreated}>
                            <Button type="text" size="small" icon={<Icon icon="Edit" className="pts:text-primary" />} />
                          </AddUpdateUserPrompt>
                          <Button
                            type="text"
                            size="small"
                            className="pts:ml-1"
                            onMouseDown={(e) => e.stopPropagation()}
                            onClick={(e) => {
                              e.stopPropagation();
                              Modal.confirm({
                                title: "确定要删除模板吗？",
                                content: "删除后，模板将不可恢复。",
                                onOk() {
                                  handleDelPrompt(item.id);
                                },
                                okButtonProps: {
                                  danger: true,
                                },
                              });
                            }}
                            icon={<Icon icon="Delete" className="pts:text-primary" />}
                          />
                        </div>
                      }
                    />
                  </Col>
                </SortableItem>
              ))}
            </Row>
          </Spin>
        </SortableList>
      </div>
    </div>
  );
};

export default TemplateModal;
