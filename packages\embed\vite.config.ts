import { resolve } from "path";

import { defineConfig } from "vite";
import { viteStaticCopy } from "vite-plugin-static-copy";
import svgr from "vite-plugin-svgr";

import typescript from "@rollup/plugin-typescript";
import tailwindcss from "@tailwindcss/vite";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  plugins: [
    typescript({
      tsconfig: resolve(__dirname, mode === "development" ? "tsconfig.json" : "tsconfig.build.json"),
    }),
    tailwindcss(),
    svgr({
      include: "**/*.svg?react",
    }),
    viteStaticCopy({
      targets: [
        {
          src: "node_modules/@cscs-agent/core/dist/agent-tailwind.css",
          dest: "",
        },
        {
          src: "node_modules/@cscs-agent/presets/dist/presets-tailwind.css",
          dest: "",
        },
        {
          src: "node_modules/@cscs-agent/icons/dist/icons.css",
          dest: "",
        },
      ],
    }),
  ],
  build: {
    lib: {
      entry: resolve(__dirname, "src/index.ts"),
      name: "@cscs-agent/embed",
      fileName: (format) => `index.${format}.js`,
      formats: ["es"],
    },
    sourcemap: mode === "development",
    outDir: "dist",
    rollupOptions: {
      // Make sure to externalize deps that shouldn't be bundled
      external: ["react", "react-dom", "antd", "@ant-design/icons"],
      output: {},
    },
  },
  resolve: {
    alias: {
      "@": resolve(__dirname, "src"),
    },
  },
}));
