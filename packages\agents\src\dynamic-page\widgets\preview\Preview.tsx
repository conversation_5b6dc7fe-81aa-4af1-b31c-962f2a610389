import { But<PERSON>, Divider, Dropdown, Modal, message } from "antd";
import dayjs from "dayjs";
import React, { useEffect, useMemo, useState } from "react";

import { CloseOutlined, SaveOutlined } from "@ant-design/icons";
import { BuildInCommand, get, post, useCommandRunner } from "@cscs-agent/core";
import { Icon } from "@cscs-agent/icons";
import { PresetsCommand } from "@cscs-agent/presets";

interface PreviewProps {
  id: string;
  saveApiUrl: string | (() => string);
  previewUrl: string | ((id: string) => string);
}

interface StandardResponse {
  success: boolean;
  errorMessage?: string;
  data?: any;
}

interface VersionData {
  version: number;
  dynamicId: string;
  dynamicName: string;
  createTime: string;
}

const Preview: React.FC<PreviewProps> = (props) => {
  const { id, saveApiUrl, previewUrl } = props ?? {};
  const runner = useCommandRunner();
  const [versionList, setVersionList] = useState<VersionData[]>([]);
  const [defaultSelectedKeys, setDefaultSelectedKeys] = useState<string[]>([id]);
  const [finalPreviewUrl, setFinalPreviewUrl] = useState("");
  const [currentId, setCurrentId] = useState(id);

  useEffect(() => {
    if (typeof previewUrl === "function") {
      setFinalPreviewUrl(previewUrl(currentId));
    } else {
      setFinalPreviewUrl(previewUrl);
    }
  }, [previewUrl, currentId]);

  useEffect(() => {
    if (currentId) {
      getVersionList();
    }
  }, []);

  const { createTime, name } = useMemo(() => {
    const item = versionList.find((i) => i.dynamicId.replace("span:page:dynamic:id:", "") === currentId);
    const createTime = item?.createTime ? dayjs(item?.createTime).format("YYYY-MM-DD HH:mm") : "";
    const name = item?.dynamicName;

    if (item?.dynamicId) {
      setDefaultSelectedKeys([item?.dynamicId]);
    }

    return { createTime, name };
  }, [currentId, versionList]);

  const save = () => {
    const url = typeof saveApiUrl === "function" ? saveApiUrl() : saveApiUrl;

    Modal.confirm({
      title: <span className="ats:text-black-85">是否将当前页面保存到系统中？</span>,
      content: <span className="ats:text-black-65">当前页面将添加到系统的“动态页面管理”中</span>,
      onOk() {
        post<StandardResponse>(url, { dynamicId: currentId })
          .then((res) => {
            if (!res.data.success) {
              message.error(res.data.errorMessage);
            } else {
              message.success("保存成功");
            }
          })
          .catch((error) => {
            message.error(error.message);
          });
      },
    });
  };

  const items = useMemo(() => {
    return versionList.map((i, index) => {
      const _createTime = dayjs(i.createTime).format("MM-DD HH:mm");
      return {
        key: i.dynamicId,
        label: (
          <div onClick={() => handleClick(i.dynamicId)}>
            <div className="ats:text-black-85 ats:text-sm">{index === 0 ? "最新版" : `V${i.version}`}</div>
            <div className="ats:text-black-45 ats:text-xs">创建时间：{_createTime}</div>
          </div>
        ),
      };
    });
  }, [versionList]);

  const getVersionList = () => {
    // const _id = id.split("_")[0];
    get("/gateway/cluster/page/system/dynamic/page/manage/getTempDynamicVersionForAi/" + currentId).then((res) => {
      const list = res.data.data;
      if (Array.isArray(list)) {
        list.reverse();
      }
      setVersionList(list);
    });
  };

  const close = () => {
    runner(BuildInCommand.CloseSidePanel);
    runner(PresetsCommand.OpenSideBar);
  };

  const handleClick = (id: string) => {
    setDefaultSelectedKeys([id]);
    setCurrentId(id);
    if (typeof previewUrl === "function") {
      setFinalPreviewUrl(previewUrl(id));
    } else {
      setFinalPreviewUrl(previewUrl);
    }
  };

  return (
    <div className="ats:flex ats:flex-col ats:w-full ats:h-full">
      <div className="ats:top-0 ats:flex ats:justify-between ats:items-center ats:bg-white ats:px-6 ats:py-3 ats:border-gray-100 ats:border-b">
        <div>
          <span className="ats:text-black-85">{name}</span>
          {createTime && <span className="ats:pl-6 ats:text-black-45 ats:text-sm">创建时间：{createTime}</span>}
        </div>
        <div className="ats:flex ats:items-center">
          <Dropdown menu={{ items, selectable: true, defaultSelectedKeys: defaultSelectedKeys }}>
            <Button size="small" type="text">
              <Icon icon="History" />
            </Button>
          </Dropdown>
          <Button variant="text" onClick={save} color="primary" icon={<SaveOutlined />} size="small">
            保存
          </Button>
          <Divider type="vertical" />
          <Button
            variant="text"
            onClick={close}
            color="default"
            icon={<CloseOutlined style={{ color: "rgba(37,45,62,0.45)" }} />}
            size="small"
          />
        </div>
      </div>
      <div className="ats:flex-1">
        {finalPreviewUrl && <iframe src={finalPreviewUrl} width={500} className="ats:w-full ats:h-full" />}
      </div>
    </div>
  );
};

export default Preview;
