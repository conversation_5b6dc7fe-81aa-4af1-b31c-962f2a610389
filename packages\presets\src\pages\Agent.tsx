import React, { useContext, useEffect } from "react";
import { useParams } from "react-router-dom";

import NavigationBar from "@/components/navigation-bar";
import { DefaultAgentLayout } from "@/layout";
import {
  AgentChatContext,
  BuildInCommand,
  Sender,
  useActiveAgentCode,
  useActiveAgentMenuCode,
  useCommandRunner,
} from "@cscs-agent/core";

const AgentHome: React.FC = () => {
  const [activeAgentMenuCode, setActiveAgentMenuCode] = useActiveAgentMenuCode();
  const [, setActiveAgentCode] = useActiveAgentCode();
  const params = useParams();
  const { config } = useContext(AgentChatContext);
  const runner = useCommandRunner();

  useEffect(() => {
    if (params.code === activeAgentMenuCode) return;
    const agents = config.agents ?? [];
    const code = agents.find((i) => i.code === params.code)?.code;
    if (code) {
      setActiveAgentMenuCode(code);
      setActiveAgentCode(code);
    }
  }, [params, config.agents]);

  useEffect(() => {
    const search = new URLSearchParams(window.location.search);
    const sendMessage = search.get("sendMessage");
    if (sendMessage) {
      setTimeout(() => {
        runner(BuildInCommand.SendMessage, { message: sendMessage, isNewConversation: true, agentCode: params.code });
      }, 100);
    }
    return () => {
      setActiveAgentMenuCode(null);
      setActiveAgentCode(null);
    };
  }, []);

  return <DefaultAgentLayout sender={<Sender isNewConversation={true} />} navigationBar={<NavigationBar />} />;
};

export default AgentHome;
