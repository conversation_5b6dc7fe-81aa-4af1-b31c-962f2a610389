import { ConfigProvider } from "antd";
import zhC<PERSON> from "antd/locale/zh_CN";
import React from "react";
import ReactDOM from "react-dom";
import { RouterProvider } from "react-router-dom";

import {
  DefaultErrorHandlerOption,
  DefaultRequestInterceptorOptions,
  setDefaultErrorHandlerOptions,
  setDefaultRequestInterceptorOptions,
} from "@/request";
import { RouterConfig, createDefaultRouter } from "@/router";
import { antdThemeTokens } from "@/theme/antd-theme";
import { AgentChatConfig } from "@/types";
import { setBaseUrl } from "@/utils";

import { setToken } from "../common/token";
import { setLoginUrl } from "../common/vars";

let initialed = false;

export interface InitAppOptions {
  defaultRequestInterceptorOptions?: Partial<DefaultRequestInterceptorOptions>;
  defaultErrorHandlerOption?: Partial<DefaultErrorHandlerOption>;
  baseUrl?: string;
  loginUrl?: string;
  routerConfig?: RouterConfig;
  customCreateAppRoot?: () => void;
  agentChatConfig?: AgentChatConfig;
  logoShow?: boolean;
  lifecycle?: {
    onBeforeInit?: () => Promise<void> | void;
    onAfterInit?: () => Promise<void> | void;
    onError?: (error: Error) => Promise<void> | void;
  };
  embedded?: boolean;
}

export async function initApp(options: InitAppOptions) {
  const {
    routerConfig,
    defaultRequestInterceptorOptions,
    defaultErrorHandlerOption,
    baseUrl,
    loginUrl,
    customCreateAppRoot,
    agentChatConfig,
    logoShow = true,
    lifecycle,
    embedded = false,
  } = options;
  const { onBeforeInit, onAfterInit, onError } = lifecycle || {};

  if (embedded && initialed) {
    console.error("Cannot initialize repeatedly!");
    return;
  }

  if (logoShow) {
    printLogoShow();
  }

  try {
    if (onBeforeInit) {
      await onBeforeInit();
    }

    loadTokenFromUrl();
    // 设置默认请求拦截器选项
    if (defaultRequestInterceptorOptions) {
      setDefaultRequestInterceptorOptions(defaultRequestInterceptorOptions);
    }
    // 设置默认错误处理选项
    if (defaultErrorHandlerOption) {
      setDefaultErrorHandlerOptions(defaultErrorHandlerOption);
    }
    // 设置基础 URL 和登录 URL
    if (baseUrl) {
      setBaseUrl(baseUrl);
    }
    // 设置登录 URL
    if (loginUrl) {
      setLoginUrl(loginUrl);
    }

    if (!embedded) {
      if (!agentChatConfig) {
        console.error("Missing agentChatConfig!");
        return;
      }
      // 初始化路由
      if (!customCreateAppRoot) {
        if (routerConfig) {
          await createAppRoot(routerConfig, agentChatConfig);
        } else {
          console.error("Router is not defined");
        }
      } else {
        customCreateAppRoot();
      }
    }

    if (onAfterInit) {
      await onAfterInit();
    }

    if (embedded) {
      initialed = true;
    }
  } catch (error) {
    if (onError) {
      await onError(error as Error);
    } else {
      console.error("App initialization error:", error);
    }
  }
}

async function createAppRoot(routerConfig: RouterConfig, agentChatConfig: AgentChatConfig) {
  const router = await createDefaultRouter(routerConfig, agentChatConfig);

  ReactDOM.render(
    <ConfigProvider
      locale={zhCN}
      theme={{
        cssVar: true,
        hashed: false,
        token: {
          ...antdThemeTokens,
        },
      }}
    >
      <RouterProvider router={router}></RouterProvider>
    </ConfigProvider>,
    document.getElementById("root"),
  );
}

function loadTokenFromUrl() {
  const urlParams = new URLSearchParams(window.location.search);
  const token = urlParams.get("token");
  if (token) {
    setToken(token);
  }
}

function printLogoShow() {
  const logo = `
  ████     ████   █████ ██    ██ █████        █ █
██    ██ ██         ██       ███  ██    ██    █    █ █
██████ ██  ███ █████ ██ █ ██    ██         █ █
██    ██ ██    ██ ██       ██  ███    ██    █  █ █
██    ██   ████   █████ ██    ██    ██       █ █
  `;
  console.log(logo);
}
