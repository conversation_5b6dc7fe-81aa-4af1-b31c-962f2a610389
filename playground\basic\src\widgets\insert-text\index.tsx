import { Button, Space } from "antd";
import React from "react";
import { EditOutlined } from "@ant-design/icons";
import { BuildInCommand, useCommandRunner } from "@cscs-agent/core";

const template = `生成一个页面，查询数据库中的<embedded-editable-tag placeholder="表名">企业信息表</embedded-editable-tag>，
在页面列表中展示 <embedded-editable-tag placeholder="字段名">企业名称</embedded-editable-tag>、<embedded-editable-tag placeholder="字段名">企业性质</embedded-editable-tag>、<embedded-editable-tag placeholder="字段名">统一社会信用代码</embedded-editable-tag>。
<embedded-select
  placeholder="请选择查询模式"
  options='${JSON.stringify([
    { label: "精确查询", value: "精确查询" },
    { label: "模糊查询", value: "模糊查询" },
  ])}'
  defaultValue="精确查询"
  tooltips="请选择页面类型"
></embedded-select>
<embedded-tag content="企业名称" rawValue="企业名称(id: 123456)" tooltips="点击查看企业详情"></embedded-tag> <embedded-datepicker placeholder="请选择日期" defaultValue="" tooltips="选择一个日期" disabled="false" format="YYYY-MM-DD" showTime="false" allowClear="true"></embedded-datepicker> <embedded-rangepicker placeholder="开始日期,结束日期" defaultValue="," tooltips="选择日期范围" disabled="false"></embedded-rangepicker>
`;

const InsertText: React.FC = () => {
  const runner = useCommandRunner();

  return (
    <Space>
      <Button
        onClick={() =>
          runner(BuildInCommand.InsertTextIntoSender, {
            text: template,
          })
        }
        size="small"
        icon={<EditOutlined />}
        shape="round"
      >
        插入模板
      </Button>
      <Button onClick={() => runner(BuildInCommand.ClearSender)} size="small" icon={<EditOutlined />} shape="round">
        清空
      </Button>
      <Button
        onClick={() => runner(BuildInCommand.UpdateSenderText, { text: "替换后的文本" })}
        size="small"
        icon={<EditOutlined />}
        shape="round"
      >
        替换文本
      </Button>
    </Space>
  );
};

export default InsertText;
