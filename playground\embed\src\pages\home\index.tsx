import { Col, Row } from "antd";
import React from "react";
import { AgentEmbedProvider, ChatEmbed } from "@cscs-agent/embed";

import { config } from "../../agent-config";

const Home = () => {
  return (
    <Row>
      <Col span={12}>
        <AgentEmbedProvider agentChatConfig={config} agentCode="dynamic-page-creator">
          <ChatEmbed />
        </AgentEmbedProvider>
      </Col>
      <Col span={12}>
        {/* <AgentEmbedProvider agentChatConfig={config} agentCode="default">
          <ChatEmbed />
        </AgentEmbedProvider> */}
      </Col>
    </Row>
  );
};

export default Home;
